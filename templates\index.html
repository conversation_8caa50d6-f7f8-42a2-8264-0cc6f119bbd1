{% extends "base.html" %}

{% block title %}Guild Roster - Uproar{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-users text-accent me-2"></i>
                    Guild Roster
                </h1> 
            </div>
            <div class="d-flex justify-content-between align-items-center w-50">
                <!-- Left: Stats -->
                <div class="d-flex align-items-center gap-3">
                    
                    <div class="text-center">
                        <div class="h5 text-gold mb-0">
                            <span id="avgIlvl">0</span>
                        </div>
                        <small class="text-secondary">Avg iLvl</small>
                    </div>
                </div>

                <!-- Center: Role Composition -->
                <div class="d-flex align-items-center gap-2">
                    <div class="role-mini tank">
                        <i class="fas fa-shield-alt"></i>
                        <span class="role-tank-count">0</span>
                    </div>
                    <div class="role-mini healer">
                        <i class="fas fa-heart"></i>
                        <span class="role-healer-count">0</span>
                    </div>
                    <div class="role-mini melee">
                        <i class="fas fa-fist-raised"></i>
                        <span class="role-melee-count">0</span>
                    </div>
                    <div class="role-mini ranged">
                        <i class="fas fa-magic"></i>
                        <span class="role-ranged-count">0</span>
                    </div>
                </div>

                <!-- Right: Buffs/Debuffs & Refresh -->
                <div class="d-flex align-items-center gap-3">
                    <div class="d-flex align-items-center gap-2">
                        <div class="buff-counter buff-druid" data-class="Druid" data-bs-toggle="tooltip" title="Mark of the Wild">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_regeneration.jpg" alt="Mark of the Wild" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-evoker" data-class="Evoker" data-bs-toggle="tooltip" title="Blessing of the Bronze">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_evoker_blessingofthebronze.jpg" alt="Blessing of the Bronze" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-hunter" data-class="Hunter" data-bs-toggle="tooltip" title="Hunter's Mark">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_hunter_snipershot.jpg" alt="Hunter's Mark" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-mage" data-class="Mage" data-bs-toggle="tooltip" title="Arcane Intellect">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_magicalsentry.jpg" alt="Arcane Intellect" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-priest" data-class="Priest" data-bs-toggle="tooltip" title="Power Word: Fortitude">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_wordfortitude.jpg" alt="Power Word: Fortitude" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-shaman" data-class="Shaman" data-bs-toggle="tooltip" title="Skyfury">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/achievement_raidprimalist_windelemental.jpg" alt="Skyfury" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-warrior" data-class="Warrior" data-bs-toggle="tooltip" title="Battle Shout">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_warrior_battleshout.jpg" alt="Battle Shout" class="buff-icon-mini">
                        </div>
                        <div class="debuff-counter debuff-demonhunter" data-class="Demon Hunter" data-bs-toggle="tooltip" title="Chaos Brand">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_demonhunter_empowerwards.jpg" alt="Chaos Brand" class="debuff-icon-mini">
                        </div>
                        <div class="debuff-counter debuff-monk" data-class="Monk" data-bs-toggle="tooltip" title="Mystic Touch">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_monk_palmstrike.jpg" alt="Mystic Touch" class="debuff-icon-mini">
                        </div>
                    </div>

                    
                </div>
            </div>
        </div>
    </div>
</div>





<!-- Character Roster Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Character Roster
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search characters..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="character-name">
                                    <i class="fas fa-user me-1"></i>Character
                                </th>
                                <th data-sort="ilvl">
                                    <i class="fas fa-star me-1"></i>iLvl
                                </th>
                                <th data-sort="role">
                                    <i class="fas fa-users-cog me-1"></i>Role
                                </th>
                                <th data-sort="class">
                                    <i class="fas fa-fist-raised me-1"></i>Class
                                </th>
                                <th data-sort="spec">
                                    <i class="fas fa-cog me-1"></i>Spec
                                </th>
                                <th data-sort="armor">
                                    <i class="fas fa-tshirt me-1"></i>Armor
                                </th>
                                <th data-sort="tier-pieces">
                                    <i class="fas fa-layer-group me-1"></i>Tier
                                </th>
                                <th data-sort="tier">
                                    <i class="fas fa-gem me-1"></i>Tier Token
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for character in characters %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if character.url and character.url != '#' %}
                                            <a href="{{ character.url }}" target="_blank"
                                               class="text-decoration-none text-primary fw-bold">
                                                {{ character.name }}
                                                <i class="fas fa-external-link-alt ms-1 small"></i>
                                            </a>
                                        {% else %}
                                            <span class="fw-bold">{{ character.name }}</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="ilvl">{{ character.ilvl }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if character.role == "Tank" %}
                                            <i class="fas fa-shield-alt text-warning me-2"></i>
                                        {% elif character.role == "Healer" %}
                                            <i class="fas fa-heart text-success me-2"></i>
                                        {% elif character.role == "Melee DPS" or character.role == "Melee" %}
                                            <i class="fas fa-fist-raised text-danger me-2"></i>
                                        {% else %}
                                            <i class="fas fa-magic text-info me-2"></i>
                                        {% endif %}
                                        <span class="role-{{ character.role.lower().replace(' ', '-') }}">
                                            {{ character.role }}
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <span class="class-{{ character.class_name.lower().replace(' ', '-') }} fw-bold">
                                        {{ character.class_name }}
                                    </span>
                                </td>
                                <td>{{ character.spec }}</td>
                                <td>
                                    <span class="badge bg-tertiary border-custom">
                                        {{ character.armor_type }}
                                    </span>
                                </td>
                                <td>
                                    <div class="tier-visual d-flex align-items-center gap-1">
                                        {% for i in range(5) %}
                                            <div class="tier-block {% if i < character.tier_pieces %}{% if character.tier_pieces >= 4 %}tier-filled-green{% else %}tier-filled-red{% endif %}{% else %}tier-empty{% endif %}"></div>
                                        {% endfor %}
                                        <span class="tier-count ms-2 small text-secondary">{{ character.tier_pieces }}/5</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-tertiary border-custom">
                                        {{ character.tier_token }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Modern counter update function
function updateCounters() {
    const rows = document.querySelectorAll('.table-modern tbody tr:not([style*="display: none"])');
    const roleCounters = {
        'Tank': 0,
        'Healer': 0,
        'Melee': 0,
        'Ranged': 0
    };


    const availableClasses = new Set();
    let totalIlvl = 0;
    let characterCount = 0;

    rows.forEach(row => {
        // Count roles (now column 2 instead of 3)
        const roleCell = row.cells[2]; // Role column
        const roleText = roleCell.textContent.trim();

        if (roleText.includes('Tank')) roleCounters['Tank']++;
        else if (roleText.includes('Healer')) roleCounters['Healer']++;
        else if (roleText.includes('Melee')) roleCounters['Melee']++;
        else if (roleText.includes('Ranged')) roleCounters['Ranged']++;



        // Track available classes for buff/debuff counters (now column 3 instead of 4)
        const classCell = row.cells[3]; // Class column
        const className = classCell.textContent.trim();
        availableClasses.add(className);

        // Calculate average ilvl (now column 1 instead of 2)
        const ilvlCell = row.cells[1]; // iLvl column
        if (ilvlCell) {
            const ilvl = parseInt(ilvlCell.textContent.trim());
            if (!isNaN(ilvl)) {
                totalIlvl += ilvl;
                characterCount++;
            }
        }
    });

    // Update role counters in stats cards
    document.querySelector('.role-tank-count').textContent = roleCounters['Tank'];
    document.querySelector('.role-healer-count').textContent = roleCounters['Healer'];
    document.querySelector('.role-melee-count').textContent = roleCounters['Melee'];
    document.querySelector('.role-ranged-count').textContent = roleCounters['Ranged'];



    // Update average ilvl
    const avgIlvl = characterCount > 0 ? Math.round(totalIlvl / characterCount) : 0;
    const ilvlDisplay = document.getElementById('avgIlvl');
    if (ilvlDisplay) {
        ilvlDisplay.textContent = avgIlvl;
    }

    // Update buff/debuff counters
    const buffCounters = document.querySelectorAll('.buff-counter, .debuff-counter');
    buffCounters.forEach(counter => {
        const requiredClass = counter.dataset.class;
        if (availableClasses.has(requiredClass)) {
            counter.classList.remove('unavailable');
            counter.classList.add('available');
        } else {
            counter.classList.remove('available');
            counter.classList.add('unavailable');
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize buff counters as unavailable
    const buffCounters = document.querySelectorAll('.buff-counter, .debuff-counter');
    buffCounters.forEach(counter => {
        counter.classList.add('unavailable');
    });

    // Update counters initially
    updateCounters();

    // Sort table by role initially
    const table = document.querySelector('.table-modern');
    if (table) {
        const roleHeader = table.querySelector('[data-sort="role"]');
        if (roleHeader) {
            roleHeader.click();
        }
    }
});
</script>

<!-- Additional CSS for buff/debuff styling -->
<style>
/* Mini Role Counters */
.role-mini {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.role-mini:hover {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.role-mini i {
    font-size: 0.75rem;
    opacity: 0.8;
}

.role-mini.tank { color: var(--role-tank, #f39c12); }
.role-mini.healer { color: var(--role-healer, #27ae60); }
.role-mini.melee { color: var(--role-melee, #e74c3c); }
.role-mini.ranged { color: var(--role-ranged, #3498db); }

/* Mini Buff/Debuff Icons */
.buff-icon-mini, .debuff-icon-mini {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    object-fit: cover;
}

.buff-counter, .debuff-counter {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
}


.buff-counter.available, .debuff-counter.available {
    border-color: #28a745;
    box-shadow: 0 0 6px rgba(40, 167, 69, 0.4);
    background: rgba(40, 167, 69, 0.1);
}

.buff-counter.unavailable, .debuff-counter.unavailable {
    border-color: #444 !important;
    background: rgba(0, 0, 0, 0.8) !important;
    opacity: 0.3 !important;
    box-shadow: none !important;
}

.buff-icon, .debuff-icon {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    object-fit: cover;
}

.buff-counter.unavailable .buff-icon,
.debuff-counter.unavailable .debuff-icon,
.buff-counter.unavailable .buff-icon-mini,
.debuff-counter.unavailable .debuff-icon-mini {
    filter: grayscale(100%) brightness(0.3) !important;
}



/* Tier visualization blocks */
.tier-visual {
    min-width: 120px;
}

.tier-block {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.tier-filled-red {
    background-color: #dc3545;
    border-color: #dc3545;
    box-shadow: 0 0 4px rgba(220, 53, 69, 0.4);
}

.tier-filled-green {
    background-color: #28a745;
    border-color: #28a745;
    box-shadow: 0 0 4px rgba(40, 167, 69, 0.4);
}

.tier-empty {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.tier-count {
    font-weight: 500;
    min-width: 30px;
}
</style>
{% endblock %}
